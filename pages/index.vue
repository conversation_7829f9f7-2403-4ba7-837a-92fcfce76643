<template>
  <transition mode="out-in" name="fade">
    <div v-if="isLoading">
      <SkeletonMarketplace></SkeletonMarketplace>
    </div>

    <div v-else class="min-h-screen text-white">
      <div class="mt-4 mb-10 lg:mb-20 lg:hidden">
        <swiper style="
            --swiper-pagination-color: #ffffff;
            --swiper-pagination-bullet-inactive-color: gray;
            --swiper-pagination-bullet-border-radius: 10px;
            --swiper-pagination-bullet-horizontal-gap: 5px;

            --swiper-pagination-bullet-width: 12px;
            --swiper-pagination-bullet-height: 3px;
            --swiper-pagination-bullet-inactive-width: 12px;
          " :autoplay="{
            delay: 5000,
          }" :pagination="true" :space-between="10" :modules="[Pagination, Autoplay]" :loop="true" :slidesPerView="1"
          :breakpoints="{
            325: {
              slidesPerView: 1,
              spaceBetween: 10,
            },
            440: {
              slidesPerView: 1.25,
              spaceBetween: 10,
            },
            768: {
              slidesPerView: 1.5,
              spaceBetween: 10,
            },
          }">
          <swiper-slide v-for="banner in mobileBanners">
            <div class="flex items-center justify-center pb-10">
              <img v-if="!banner.url" class="flex object-contain h-auto min-w-full md:w-full" :src="banner.image"
                alt="" />
              <nuxt-link v-else :href="banner.url" :target="banner.url_target">
                <img class="flex object-contain h-auto min-w-full md:w-full" :src="banner.image" alt="" />
              </nuxt-link>
            </div>
          </swiper-slide>
        </swiper>
      </div>

      <div class="hidden mt-4 mb-10 lg:mb-20 lg:block">
        <swiper style="
            --swiper-pagination-color: #ffffff;
            --swiper-pagination-bullet-inactive-color: gray;
            --swiper-pagination-bullet-border-radius: 10px;
            --swiper-pagination-bullet-horizontal-gap: 5px;

            --swiper-pagination-bullet-width: 24px;
            --swiper-pagination-bullet-height: 4px;
            --swiper-pagination-bullet-inactive-width: 24px;
          " :autoplay="{
            delay: 5000,
          }" :pagination="true" :space-between="12" :modules="[Pagination, Autoplay]" :loop="true" :slidesPerView="2"
          :breakpoints="{
            1024: {
              slidesPerView: 1.25,
              spaceBetween: 20,
            },
            1280: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
          }">
          <swiper-slide v-for="banner in webBanners" class="flex w-full">
            <div class="pb-10">
              <img v-if="!banner.url" class="flex object-cover h-auto min-w-auto" :src="banner.image" alt="" />
              <nuxt-link v-else :href="banner.url" :target="banner.url_target">
                <img class="flex object-cover h-auto min-w-auto" :src="banner.image" alt="" />
              </nuxt-link>
            </div>
          </swiper-slide>
        </swiper>
      </div>

      <div class="mb-14 lg:mb-[72px] min-w-[calc(100%+20px)]"
        v-if="bestDeals.length > 0 && useRuntimeConfig().public.bidOnly != 'true'">
        <h1 class="mb-6 text-xl font-medium lg:text-2xl lg:mb-8">Best Deals</h1>
        <div class="relative md:max-w-full">
          <swiper :space-between="12" :modules="[Navigation]" :navigation="{
            nextEl: '.swiper-button-next-1',
            prevEl: '.swiper-button-prev-1',
          }" slides-per-view="auto" :breakpoints="{
            360: {
              spaceBetween: 12,
              centeredSlides: false,
            },
            550: {
              spaceBetween: 12,
            },
            768: {
              spaceBetween: 12,
            },
            1024: {
              spaceBetween: 12,
            },
            1200: {
              spaceBetween: 16,
            },
            1440: {
              spaceBetween: 20,
            },
            1600: {
              spaceBetween: 24,
            },
            1920: {
              spaceBetween: 24,
            },
          }" class="w-full">
            <!-- @click.prevent.stop="
                  web3Store.isLogin ? showBuyModal(lot) : showLoginModal()
                " -->
            <!-- @click="toLotInfo(lot)" -->
            <!-- @showBuyModalEvent="web3Store.isLogin ? showBuyModal(lot) : showLoginModal()" -->
            <swiper-slide
              v-for="(lot, index) in bestDeals"
              :key="index"
              class="min-w-[295px] max-w-[295px]"
            >
              <CardLot
                @showBuyModalEvent="
                  isLoggedIn ? showBuyModal($event.lot || lot) : showLoginModal()
                "
                :tokenDecimal="lot.token_decimal"
                :tokenImg="lot.token_image"
                :networkImg="lot.network_image"
                :networkSymbol="lot.network_symbol"
                :totalListing="lot.total_listing"
                :remainingListing="lot.remaining_listing"
                :tokenName="lot.token_name"
                :tokenTicker="lot.token_ticker"
                :listId="lot.list_id"
                :listingType="lot.listing_type"
                :tokenPrice="
                  divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS).toString()
                "
                :bestPrice="divideNumberUsingDecimals(
                  lot.best_price, 
                  USDT_DECIMALS
                ).toString()"
                :maxDiscount="lot.max_discount"
                :unlockStart="lot.unlock_start"
                :displayId="lot.display_id"
                :twfHourChanges="`${
                  twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
                }${lot.twf_hour_changes.replace('-', '')}%`"
                :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color"
                :lotId="common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id)"></CardLot>
            </swiper-slide>
          </swiper>
          <!-- Navigation Buttons -->
          <button v-if="bestDeals.length > 4"
            class="swiper-button-prev swiper-button-prev-1 absolute !left-[-40px] z-50">
            <inline-svg class="w-[24px] h-[24px]" :src="arrowLeftIcon"></inline-svg>
          </button>
          <button v-if="bestDeals.length > 4"
            class="swiper-button-next swiper-button-next-1 absolute !right-[-40px] z-50">
            <inline-svg class="w-[24px] h-[24px]" :src="arrowRightIcon"></inline-svg>
          </button>
        </div>
      </div>

      <!-- Temporary add "false &&" to hide this section https://hoasendigital.slack.com/archives/C08T1CYJU3W/p1749114944560369?thread_ts=1749091679.092849&cid=C08T1CYJU3W -->
      <div class="mb-14 lg:mb-[72px] min-w-[calc(100%+20px)]"
        v-if="false && unlockSoon.length > 0 && useRuntimeConfig().public.bidOnly != 'true'">
        <h1 class="mb-5 text-xl font-medium lg:text-2xl lg:mb-8">Unlocking Soon</h1>
        <div class="relative max-w-full">
          <swiper
            class="w-full"
            :modules="[Navigation]"
            :navigation="{
              nextEl: '.swiper-button-next-2',
              prevEl: '.swiper-button-prev-2',
            }"
            slides-per-view="auto"
            :breakpoints="{
              360: {
                spaceBetween: 12,
                centeredSlides: false,
              },
              550: {
                spaceBetween: 12,
              },
              768: {
                spaceBetween: 12,
              },
              1024: {
                spaceBetween: 12,
              },
              1200: {
                spaceBetween: 16,
              },
              1440: {
                spaceBetween: 20,
              },
              1600: {
                spaceBetween: 24,
              },
              1920: {
                spaceBetween: 24,
              },
            }"
          >
            <swiper-slide
              v-for="(lot, index) in unlockSoon"
              :key="index"
              class="min-w-[295px] max-w-[295px]"
            >
              <CardLot
                @showBuyModalEvent="
                  isLoggedIn ? showBuyModal($event.lot || lot) : showLoginModal()
                "
                :tokenDecimal="lot.token_decimal"
                :tokenImg="lot.token_image"
                :networkImg="lot.network_image"
                :networkSymbol="lot.network_symbol"
                :totalListing="lot.total_listing"
                :remainingListing="lot.remaining_listing"
                :tokenName="lot.token_name"
                :tokenTicker="lot.token_ticker"
                :listingType="lot.listing_type"
                :listId="lot.list_id"
                :tokenPrice="
                  divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS).toString()
                "
                :bestPrice="divideNumberUsingDecimals(
                  lot.best_price,
                  USDT_DECIMALS
                ).toString()"
                :maxDiscount="lot.max_discount"
                :unlockStart="lot.unlock_start"
                :displayId="lot.display_id"
                :twfHourChanges="`${
                  twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
                }${lot.twf_hour_changes.replace('-', '')}%`"
                :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color"
                :lotId="common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id)"></CardLot>
              <!-- <ModalMarketplaceBuy
                :lot="selectedLot"
                ref="buyModal"
              ></ModalMarketplaceBuy> -->
            </swiper-slide>
          </swiper>
          <!-- Navigation Buttons -->
          <button v-if="unlockSoon.length > 4"
            class="swiper-button-prev swiper-button-prev-2 absolute !left-[-40px] z-50">
            <inline-svg class="w-[24px] h-[24px]" :src="arrowLeftIcon"></inline-svg>
          </button>
          <button v-if="unlockSoon.length > 4"
            class="swiper-button-next swiper-button-next-2 absolute !right-[-40px] z-50">
            <inline-svg class="w-[24px] h-[24px]" :src="arrowRightIcon"></inline-svg>
          </button>
        </div>
      </div>

      <!-- Temporary add "false &&" to hide this section https://linear.app/secondswap/issue/SEC-567/marketplace-updates  -->
      <div class="mb-16 lg:mb-[72px] min-w-[calc(100%+20px)]"
        v-if="false && topTokens.length > 0 && useRuntimeConfig().public.bidOnly != 'true'">
        <h1 class="mb-1 text-xl font-medium lg:text-2xl lg:mb-5">Top Token</h1>

        <div class="relative max-w-full">
          <swiper
            :modules="[Navigation]"
            :navigation="{
              nextEl: '.swiper-button-next-3',
              prevEl: '.swiper-button-prev-3',
            }"
            slides-per-view="auto"
            :breakpoints="{
              360: {
                spaceBetween: 12,
                centeredSlides: false,
                slidesOffsetBefore: 0,
                slidesOffsetAfter: 30,
              },
              550: {
                spaceBetween: 12,
              },
              768: {
                spaceBetween: 12,
              },
              1024: {
                spaceBetween: 12,
              },
              1200: {
                spaceBetween: 16,
              },
              1440: {
                spaceBetween: 20,
              },
              1600: {
                spaceBetween: 24,
              },
              1920: {
                spaceBetween: 24,
              },
            }"
            class="w-full"
          >
            <swiper-slide
              v-for="(token, index) in topTokens"
              :key="index"
              class="min-w-[295px] max-w-[295px] pt-4"
            >
              <nuxt-link
                :to="`/top-token/${token.token_address}?network=${token.network_symbol}`"
              >
                <CardToken
                  :tokenImg="token.token_image"
                  :tokenDecimal="token.token_decimal"
                  :networkImg="token.network_image"
                  :tokenPrice="
                    divideNumberUsingDecimals(token.token_price, USDT_DECIMALS).toString()
                  "
                  :tokenName="token.token_name"
                  :tokenTicker="token.token_ticker"
                  :totalAvailable="
                    divideNumberUsingDecimals(
                      token.total_available,
                      token.token_decimal
                    ).toString()
                  "
                  :marketCap="
                    divideNumberUsingDecimals(token.total_supply, token.token_decimal).toString()
                  "
                  :shortestUnlock="token.earliest_unlock"
                  :twfHourChanges="`${
                    twfChangesInfo[token.twf_hour_changes * 1 > 0].symbol
                  }${token.twf_hour_changes.replace('-', '')}%`"
                  :twfHourColor="twfChangesInfo[token.twf_hour_changes * 1 > 0].color"
                ></CardToken>
              </nuxt-link>
            </swiper-slide>
          </swiper>
          <!-- Navigation Buttons -->
          <button v-if="topTokens.length > 4"
            class="swiper-button-prev swiper-button-prev-3 absolute !left-[-40px] z-50">
            <inline-svg class="w-[24px] h-[24px]" :src="arrowLeftIcon"></inline-svg>
          </button>
          <button v-if="topTokens.length > 4"
            class="swiper-button-next swiper-button-next-3 absolute !right-[-40px] z-50">
            <inline-svg class="w-[24px] h-[24px]" :src="arrowRightIcon"></inline-svg>
          </button>
        </div>
      </div>

      <div class="mb-16 lg:mb-[72px]">
        <h1 class="pb-4 mb-1 text-xl font-medium lg:text-2xl lg:mb-5">Pre-Launch tokens</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-5">
            <div
              v-for="(token, index) in bidOnlyTokens"
              :key="index"
            >
              <nuxt-link
                :to="`/bid-only-token/${token.token_ticker}/?network=${token.network_symbol}`"
              >
                <CardBidOnlyToken
                  :tokenImg="token.token_image"
                  :networkImg="token.network_image"
                  :tokenPrice="
                    divideNumberUsingDecimals(token.token_price, USDT_DECIMALS).toString()
                  "
                  :tokenName="token.token_name"
                  :tokenTicker="token.token_ticker"
                  :marketCap="
                    divideNumberUsingDecimals(token.market_cap, USDT_DECIMALS).toString()
                  "
                  :circulatingSupply="
                    divideNumberUsingDecimals(token.market_cap, USDT_DECIMALS).dividedBy(divideNumberUsingDecimals(token.token_price, USDT_DECIMALS))
                  "
                ></CardBidOnlyToken>
              </nuxt-link>
            </div>
        </div>
      </div>

      <div v-if="useRuntimeConfig().public.bidOnly != 'true'">
        <div class="flex flex-wrap items-center justify-between pb-6 gap-y-4 lg:border-b border-white/20">
          <div class="flex gap-8 md:gap-10 text-2sm">
            <h5 :class="`${activeTab == 'bestDeal' ? 'tab-active relative' : 'text-white/50'
              } relative cursor-pointer pb-0.5`" @click="activeTab = 'bestDeal'">
              Best Deal
            </h5>
            <h5 :class="`${activeTab == 'popular' ? 'tab-active relative' : 'text-white/50'
              } relative cursor-pointer`" @click="activeTab = 'popular'">
              Popular
            </h5>
            <h5 :class="`${activeTab == 'unlockingSoon' ? 'tab-active relative' : 'text-white/50'
              } relative cursor-pointer`" @click="activeTab = 'unlockingSoon'">
              Unlocking Soon
            </h5>
          </div>

          <div class="flex flex-wrap items-center gap-4">
            <label class="flex items-center gap-2 fieldset-label">
              <input v-model="showAllNetwork" type="checkbox" checked="checked"
                class="border-textbox-border rounded-2sm checkbox checkbox-primary checkbox-xs" />
              Default to all
            </label>
            <details ref="filterNetworkDropdown" v-if="!showAllNetwork" className="dropdown">
              <summary
                class="flex items-center h-10 gap-2 px-4 m-1 bg-transparent border rounded-full cursor-pointer border-textbox-border">
                <img v-if="
                  filterNetwork === mainnet.nativeCurrency.symbol ||
                  filterNetwork === sepolia.nativeCurrency.symbol
                " class="min-w-[22px] w-[22px] h-[22px]" :src="ethNetworkIcon" alt="" />
                <img v-if="filterNetwork === avalanche.nativeCurrency.symbol" class="min-w-[22px] w-[22px] h-[22px]"
                  :src="avaxNetworkIcon" alt="" />
                {{
                  web3Store.networks.find(
                    (network) => network.nativeCurrency.symbol === filterNetwork
                  )?.name
                }}
              </summary>
              <ul className="menu dropdown-content bg-base-100 rounded-box z-10 w-52 p-2 shadow-sm">
                <li v-for="network in web3Store.networks">
                  <button type="button" @click="
                    () => {
                      filterNetwork = network.nativeCurrency.symbol;
                      filterNetworkDropdownRef.removeAttribute('open');
                    }
                  ">
                    <div class="flex items-center gap-4">
                      <img v-if="network.id === mainnet.id || network.id === sepolia.id"
                        class="min-w-[22px] w-[22px] h-[22px]" :src="ethNetworkIcon" alt="" />
                      <img v-if="network.id === avalanche.id" class="min-w-[22px] w-[22px] h-[22px]"
                        :src="avaxNetworkIcon" alt="" />
                      {{ network.name }}
                    </div>
                  </button>
                </li>
              </ul>
            </details>
            <div
              class="flex items-center justify-center gap-2 rounded-full border border-white/20 lg:px-4 w-8 h-8 md:w-[220px] md:h-[40px] cursor-pointer">
              <inline-svg :src="searchIcon"
                class="hidden md:block w-4 h-4 md:min-w-[20px] md:h-5 stroke-input-icons"></inline-svg>
              <inline-svg @click="openSearchModal" :src="searchIcon"
                class="md:hidden w-4 h-4 md:min-w-[20px] stroke-input-icons"></inline-svg>
              <input
                class="hidden text-2sm md:block input h-10 min-h-10 bg-transparent border-none focus:ring-0 focus:outline-none p-0 md:w-[75%] lg:w-[90%]"
                type="text" placeholder="Search Token" @keyup="searchToken" v-model="searchTerm" />
            </div>
          </div>
        </div>

        <div class="hidden py-6 pt-4 lg:block">
          <TableLot :lots="lots" :getCountDown="getCountDown" :getAllLots="getAllLots" :twfChangesInfo="twfChangesInfo"
            @showBuyModalEvent="
              isLoggedIn ? showBuyModal($event.lot) : showLoginModal()
              " noDataContent="No Lots Available" :myAssets="false"></TableLot>

          <Paginate v-if="allLotsMeta?.itemCount ?? 0 > 10" @callback="handlePaginateUpdate()"
            :totalItems="allLotsMeta?.itemCount ?? 0" ref="paginateRef" />
        </div>

        <!-- mobile all lots -->
        <div v-if="!lots || lots.length == 0" class="lg:hidden">
          <NoData :content="'No Lots Available'"></NoData>
        </div>
        <div v-else class="pb-4 lg:hidden">
          <div v-for="(lot, index) in lots" class="w-full pb-4">
            <CardAllLot
              @click="toLotInfo(lot)"
              @showBuyModalEvent="
                isLoggedIn ? showBuyModal($event.lot || lot) : showLoginModal()
              "
              :index="index"
              :tokenImg="lot.token_image"
              :networkImg="lot.network_image"
              :networkSymbol="lot.network_symbol"
              :totalListing="lot.total_listing"
              :remainingListing="lot.remaining_listing"
              :tokenName="lot.token_name"
              :tokenTicker="lot.token_ticker"
              :tokenDecimal="lot.token_decimal"
              :listId="lot.list_id"
              :tokenPrice="
                divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS).toString()
              "
              :bestPrice="divideNumberUsingDecimals(lot.listed_price, USDT_DECIMALS).toString()"
              :maxDiscount="lot.max_discount"
              :unlockStart="
                lot.plan_function === 'CYCLE'
                  ? $dayjs(lot.start_date)
                      .add(lot.cliff_duration, timeUnit)
                      .add(lot.duration, timeUnit)
                      .format('x')
                  : $dayjs(lot.start_date)
                      .add(lot.cliff_duration, timeUnit)
                      .format('x')
              "
              :displayId="lot.display_id"
              :twfHourChanges="`${
                twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
              }${lot.twf_hour_changes.replace('-', '')}%`"
              :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color"
              :lotId="common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id)" :buttonText="'Buy'">
            </CardAllLot>
          </div>
        </div>
      </div>

      <ModalMarketplaceBuy :lot="selectedLot" ref="buyModal" @openBuySummary="handleBuySummary"
        v-if="useRuntimeConfig().public.bidOnly != 'true'"></ModalMarketplaceBuy>

      <ModalBuySummary :lot="selectedLot" :buyForm="buyModal?.buyForm" :isMarketplaceBuy="true"
        :tokenImage="selectedLot?.token_image" :tokenPrice="selectedLot?.token_price || selectedLot?.price"
        ref="newBuySummaryModal" v-if="useRuntimeConfig().public.bidOnly != 'true'"></ModalBuySummary>

      <ModalConnect ref="connectModal"></ModalConnect>

      <!-- <ModalApprove
      ref="newApproveModal"
      ></ModalApprove> -->

      <ModalSuccess ref="newSuccessModal"> </ModalSuccess>

      <ModalSearch ref="searchModalRef" v-model="isSearchModalVisible" @closeModal="closeSearchModal"
        @showBuyModal="showBuyModal" @showLoginModal="showLoginModal" :twfChangesInfo="twfChangesInfo" :lots="lots"
        v-if="useRuntimeConfig().public.bidOnly != 'true'"></ModalSearch>
    </div>
  </transition>
</template>

<script setup>
import secondSwapToken from "~/assets/images_new/tokens/2swap_1_5x.webp";
import ethNetwork from "~/assets/images_new/networks/eth_4x.webp";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import searchIcon from "~/assets/images_new/icons/search.svg";

import arrowLeftIcon from "~/assets/images_new/icons/arrow-left.svg";
import arrowRightIcon from "~/assets/images_new/icons/arrow-right.svg";
import sortIcon from "~/assets/images_new/icons/sort.svg";

import ethNetworkIcon from "~/assets/images_new/networks/eth_4x.webp";
import avaxNetworkIcon from "~/assets/images_new/networks/avax_4x.png";

import { mainnet, avalanche, sepolia } from "@reown/appkit/networks";
import numeral from "numeral";
import InlineSvg from "vue-inline-svg";
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/swiper-bundle.css";
import { Navigation, Pagination, Autoplay } from "swiper/modules";

import { useMarketplace } from "../../../composables/useMarketplace";
import { USDT_DECIMALS, TIME_UNIT } from "~/utils/const";
import { divideNumberUsingDecimals } from "~/utils/number";

definePageMeta({
  layout: "new-app",
});

useHead({
  title: "Marketplace",
});

const web3Store = useWeb3Store();
const newWeb3Store = useNewWeb3Store();
const { isLoggedIn } = storeToRefs(newWeb3Store);

const {
  mobileBanners,
  webBanners,
  isLoading,
  bestDeals,
  unlockSoon,
  topTokens,
  bidOnlyTokens,
  allLots,
  allLotsMeta,
  twfChangesInfo,
  getCountDown,
  activeTab,
  getAllLots,
} = useMarketplace();

const { $dayjs } = useNuxtApp();
const selectedLot = ref(null);
const buyModal = ref(null);
const newBuySummaryModal = ref(null);
const connectModal = ref(null);
const searchModalRef = ref(null);
const timeUnit = TIME_UNIT;

const lots = ref(null);
const sortBy = ref(null);
const order = ref(null);
const searchTerm = ref(null);
const showAllNetwork = ref(true);
const filterNetwork = ref(null);
const filterNetworkDropdownRef = useTemplateRef("filterNetworkDropdown");

const paginateRef = ref(null);

let searchTimeout;

const tabSortMap = {
  bestDeal: "BEST_PRICE",
  unlockingSoon: "UNLOCK_SOON",
  popular: "FDV",
};

const sortIconColor = {
  bestDeal: "BEST_PRICE",
  unlockingSoon: "UNLOCK_SOON",
  popular: "FDV",
};

function handleSearchToken(value) {
  console.log("handleSearchToken called with:", searchTerm);
  searchTerm.value = value;

  searchToken();
}

function showBuyModal(lot) {
  // if (lot.is_private == 1) {
  //   useSweetAlertStore().showAlert("error", "private lot", "error");
  //   return;
  // }
  console.log("lot from show buy modal", lot);
  selectedLot.value = lot;
  console.log("selected lot", selectedLot.value);

  if (lot.listing_type == 0) {
    buyModal.value.resetForm();
  }
  // alert('hii')
  setTimeout(() => {
    buyModal.value.updateOutput(lot);
    buyModal.value.handleClick();
    document.getElementById("buyModal").checked = true;
  }, 100);
}

function showLoginModal() {
  connectModal.value.handleClick();
  document.getElementById("connectModal").checked = true;
}

function showSweetAlert() {
  //useSweetAlertStore().showAlert("success", "success", "success");
  useSweetAlertStore().showBuySuccessAlert("All Done!", "Buy Success", "success");
}

async function handlePaginateUpdate() {
  lots.value = await getAllLots(
    tabSortMap[activeTab.value],
    order.value,
    searchTerm.value,
    paginateRef.value.currentPage
  );
}

function handleBuySummary() {
  console.log("handleBuySummary executed");

  newBuySummaryModal.value.handleClick();
}

const router = useRouter();
async function toLotInfo(lot) {
  console.log("to lot", lot);

  router.push(`/${lot.plan_id}-${lot.display_id}-${lot.list_id}?network=${lot.network_symbol}`);
}

watch(activeTab, async (newValue) => {
  if (allLotsMeta?.itemCount ?? 0 > 10) {
    paginateRef.value.currentPage = 1;
  }
  order.value = null;
  lots.value = await getAllLots(tabSortMap[newValue], order.value);
});

watch(showAllNetwork, async (value) => {
  if (!value) {
    filterNetwork.value =
      web3Store.getConfigByCurrentNetwork().network?.nativeCurrency.symbol ?? null;
  } else {
    filterNetwork.value = null;
  }
});

watch(filterNetwork, async (newNetwork) => {
  lots.value = await getAllLots(
    tabSortMap[activeTab.value],
    order.value,
    searchTerm.value,
    undefined,
    newNetwork
  );
});

async function searchToken() {
  // Clear any existing timeout
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  searchTimeout = setTimeout(async () => {
    console.log("search token function called with:", searchTerm.value);
    lots.value = await getAllLots(
      tabSortMap[activeTab.value],
      order.value,
      searchTerm.value,
      undefined,
      filterNetwork.value
    );
  }, 500);
}

onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
});

onMounted(async () => {
  lots.value = await getAllLots("BEST_PRICE");
  console.log("lots from mounted", allLots);
});

const isSearchModalVisible = ref(false);

function openSearchModal() {
  isSearchModalVisible.value = false;
  searchTerm.value = null;
  isSearchModalVisible.value = true;
}

async function closeSearchModal() {
  isSearchModalVisible.value = false;
  searchModalRef.value.searchTerm = null;
}
</script>

<style scoped>
.swiper-button-prev,
.swiper-button-next {
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  content: "";
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
  opacity: 0.5;
}

.sort-icon :deep(path),
.sort-asc :deep(path:last-of-type),
.sort-desc :deep(path:first-of-type) {
  fill: #a1a1a1;
}

.sort-asc :deep(path:first-of-type) {
  fill: white;
}

.sort-desc :deep(path:last-of-type) {
  fill: white;
}
</style>
