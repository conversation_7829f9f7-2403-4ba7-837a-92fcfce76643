<template>
  <transition mode="out-in" name="fade">
    <div v-if="isLoading">
      <SkeletonMyAssets></SkeletonMyAssets>
    </div>
    <div v-else>
      <!-- Net Worth Header -->
      <div class="font-medium md:py-8">
        <h5 class="flex items-center gap-1 mb-2 text-sm md:text-base text-input-icons">
          <span>Net Worth</span>
          <inline-svg :src="infoIcon"></inline-svg>
        </h5>
        <div class="flex flex-col items-start gap-3 lg:flex-row lg:items-end">
          <h1 class="text-[32px] md:text-5xl text-white font-satoshiBlack leading-[42px]">
            {{
              formatUSDT(toBigNumber(userSummary.net_worth).plus(toBigNumber(userSummary.locked_amount)).plus(toBigNumber(claimableAmountSum)))
            }}
            USD
          </h1>
          <div class="flex gap-[8px] items-end">
            <inline-svg :src="lockIcon" class="w-[18px] h-[18px] md:w-6 md:h-6"></inline-svg>
            <h5 class="flex items-center gap-1 text-input-icons text-2sm md:text-xl leading-[20px]">
              Locked Value:
              <span class="text-white">
                {{
                  formatUSDT(toBigNumber(userSummary.net_worth).plus(toBigNumber(userSummary.locked_amount)))
                }}
              </span>
              <inline-svg :src="infoIcon"></inline-svg>
            </h5>
          </div>
        </div>
      </div>

      <div class="hidden w-full h-0 mb-6 border-b border-textbox-border md:block"></div>


      <!-- Main Tab Navigation -->
      <div class="flex flex-col items-start justify-between gap-6 mt-8 md:flex-row md:items-center md:mt-6">
        <div class="flex gap-1 overflow-x-auto md:gap-2 text-2sm">
          <div v-if="false"
            :class="{ 'relative cursor-pointer whitespace-nowrap h-8 flex items-center px-2.5 md:px-5': true, 'text-primary bg-[#121D30] rounded-lg': activeTab === 'overview', 'text-white/50': activeTab !== 'overview' }"
            @click="activeTab = 'overview'">Overview</div>
          <div
            :class="{ 'relative cursor-pointer whitespace-nowrap h-8 flex items-center px-2.5 md:px-5': true, 'text-primary bg-[#121D30] rounded-lg': activeTab === 'myassets', 'text-white/50': activeTab !== 'myassets' }"
            @click="activeTab = 'myassets'">My Assets</div>
          <div
            :class="{ 'relative cursor-pointer whitespace-nowrap h-8 flex items-center px-2.5 md:px-5': true, 'text-primary bg-[#121D30] rounded-lg': activeTab === 'listing', 'text-white/50': activeTab !== 'listing' }"
            @click="activeTab = 'listing'">Listing</div>
          <!-- Activity tab is not available yet because there's no API -->
          <div v-if="false"
            :class="{ 'relative cursor-pointer whitespace-nowrap h-8 flex items-center px-2.5 md:px-5': true, 'text-primary bg-[#121D30] rounded-lg': activeTab === 'activity', 'text-white/50': activeTab !== 'activity' }"
            @click="activeTab = 'activity'">Activity</div>
        </div>

        <!-- Network Selection (shown on certain tabs) -->
        <div v-if="activeTab !== 'overview'" class="flex items-center justify-between">
          <div class="flex items-center px-3 py-1 text-white border rounded-full text-2sm border-textbox-border">
            Network:
            <div class="inline-block ml-2 dropdown dropdown-end">
              <div tabindex="0" role="button" class="flex items-center gap-2 text-white cursor-pointer">
                <img :src="getCurrentNetworkIcon()" class="w-4 h-4" />
                <span class="hidden sm:inline">{{ getCurrentNetworkName() }}</span>
                <span class="sm:hidden">{{ getCurrentNetworkName() }}</span>
                <inline-svg :src="chevronDownIcon" class="w-4 h-4"></inline-svg>
              </div>
              <ul tabindex="0" class="dropdown-content menu bg-secondary rounded-box z-[1] w-[99px] p-2 shadow">
                <li v-for="network in networks" :key="network.symbol">
                  <a @click="changeNetwork(network.symbol)" class="flex items-center gap-2">
                    <img :src="network.icon" class="w-4 h-4" />
                    {{ network.symbol }}
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="hidden w-full h-0 mt-6 border-b border-textbox-border md:block"></div>

      <!-- Tab Content -->
      <div class="mt-6 md:mt-8">
        <!-- Overview Tab -->
        <div v-if="activeTab === 'overview'" class="animate-fade-in">
          <OverviewTab :userSummary="userSummary" :claimableAmountSum="claimableAmountSum" :tokenPlans="tokenPlans"
            :formatUSDT="formatUSDT" :toBigNumber="toBigNumber" />
        </div>

        <!-- My Assets Tab -->
        <div v-if="activeTab === 'myassets'" class="animate-fade-in">
          <MyAssetsTab :network="currentNetwork" :tokenPlans="tokenPlans" :claimableAmounts="claimableAmounts" :userSummary="userSummary"
            :claimableAmountSum="claimableAmountSum" :formatUSDT="formatUSDT" :toBigNumber="toBigNumber"
            @claimEvent="claimToken" @listEvent="handleMobileListModal" @summaryEvent="handleMobilePlanDetailsModal"
            @showClaimEvent="claimToken" @showListEvent="showListModal" @showSummaryEvent="showSummaryModal" />
        </div>

        <!-- Listing Tab -->
        <div v-if="activeTab === 'listing'" class="animate-fade-in">
          <ListingTab :network="currentNetwork" :userListedLots="userListedLots"
            :userListedLotsMeta="userListedLotsMeta" :twfChangesInfo="twfChangesInfo" :getCountDown="getCountDown"
            :getUserListedLots="getUserListedLots" :delistConfirm="delistConfirm"
            :divideNumberUsingDecimals="divideNumberUsingDecimals" :USDT_DECIMALS="USDT_DECIMALS"
            @handlePaginateUpdate="handlePaginateUpdate" />
        </div>

        <!-- Activity Tab -->
        <div v-if="activeTab === 'activity'" class="animate-fade-in">
          <ActivityTab />
        </div>
      </div>

      <ModalList ref="newListModal" :lotData="selectedPlan" :usdtBalance="usdtBalance"
        @openListSummary="handleListSummary"></ModalList>

      <ModalListSummary ref="listSummaryModal" :selectedPlan="selectedPlan" :listForm="newListModal?.listForm"
        :currentNetwork="currentNetwork" @callback="initMyAssets" @openListModal="handleBackToList">
      </ModalListSummary>

      <ModalPlanDetails ref="planDetailsModal" :selectedPlan="selectedPlan"></ModalPlanDetails>
    </div>
  </transition>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import lockIcon from "~/assets/images_new/icons/lock.svg";
import chevronDownIcon from "~/assets/images_new/icons/chevron-down.svg";
import infoIcon from "~/assets/images_new/icons/info-circle.svg";
import { USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, toBigNumber } from "~/utils/number";

// Import tab components
import OverviewTab from "~/components/myassets/OverviewTab.vue";
import MyAssetsTab from "~/components/myassets/MyAssetsTab.vue";
import ListingTab from "~/components/myassets/ListingTab.vue";
import ActivityTab from "~/components/myassets/ActivityTab.vue";

// Network configuration
const networks = [
  {
    symbol: 'ETH',
    name: 'Ethereum',
    icon: 'https://files-secondswap-staging.secondswaptest.net/eth_4x.webp',
  },
  {
    symbol: 'AVAX',
    name: 'Avalanche',
    icon: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png',
  },
  {
    symbol: "SOL",
    name: "Solana",
    icon: "https://assets.coingecko.com/coins/images/4128/large/solana.png",
  },
  {
    symbol: "ZK",
    name: "ZKSync",
    icon: "https://assets.coingecko.com/coins/images/38043/large/ZKTokenBlack.png",
  }
];

const {
  getUserListedLots,
  initMyAssets,
  getCountDown,
  delistConfirm,
  claimToken,
  tokenPlans,
  claimableAmounts,
  claimableAmountSum,
  showListModal,
  showSummaryModal,
  selectedPlan,
  web3Store,
  isLoading,
  userListedLots,
  userListedLotsMeta,
  userSummary,
  twfChangesInfo,
  currentNetwork,
  changeNetwork,
} = useMyAssets();

const newListModal = ref(null);
const listSummaryModal = ref(null);
const planDetailsModal = ref(null);
const usdtBalance = ref(0);
const paginateRef = ref(null);
const activeTab = ref('myassets');

// Helper methods for network selection
const getCurrentNetworkIcon = () => {
  const network = networks.find(n => n.symbol === currentNetwork.value);
  return network ? network.icon : networks[0].icon;
};

const getCurrentNetworkName = () => {
  const network = networks.find(n => n.symbol === currentNetwork.value);
  return network ? network.symbol : networks[0].symbol;
};

onMounted(async () => {
  // Initialize with current network
});

web3Store.$subscribe(async (_, state) => {
  console.log("web3 state", state.isInit);

  if (state.isInit) {
    const config = web3Store.getConfigByCurrentNetwork()
    usdtBalance.value = await web3Store.getTokenBalance(
      config.usdtToken,
      config.network.nativeCurrency.symbol
    );
  }
});

async function handlePaginateUpdate() {
  const res = await api.apiCall("GET", "/user/listing", {
    page: paginateRef.value.currentPage,
    status: 0,
    take: 1,
    sort_by: "LISTED_TIME",
    network: currentNetwork.value.toUpperCase()
  });
  userListedLots.value = res.data.message.data;
  console.log("user listed lots pagination", userListedLots.value);
}

function handleMobileListModal(plan) {
  newListModal.value.handleClick();
  showListModal(plan);
}

function handleBackToList() {
  newListModal.value.handleClick();
}

function handleMobilePlanDetailsModal(plan) {
  planDetailsModal.value.handleClick();
  showSummaryModal(plan);
}



const handleListSummary = () => {
  listSummaryModal.value.handleClick();
};

definePageMeta({
  layout: "new-app",
  middleware: "auth",
});
</script>

<style scoped></style>
