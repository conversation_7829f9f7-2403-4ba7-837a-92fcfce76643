{"version": "2.6.0", "name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@noble/hashes": "^1.8.0", "@pinia/nuxt": "^0.5.4", "@reown/appkit": "^1.7.16", "@reown/appkit-adapter-ethers": "^1.7.16", "@reown/appkit-adapter-solana": "^1.7.16", "@secondswap/sdk": "^0.3.6", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-wallets": "^0.19.36", "@tanstack/vue-query": "^5.74.3", "@types/numeral": "^2.0.5", "animate.css": "^4.1.1", "axios": "^1.7.7", "bignumber.js": "^9.3.0", "buffer": "^6.0.3", "canvas": "^3.1.0", "chart.js": "^4.4.4", "chartjs-plugin-annotation": "^3.0.1", "clipboard": "^2.0.11", "ethers": "^6.13.2", "ethers-decode-error": "^2.1.3", "gsap": "^3.12.5", "lottie-web": "^5.12.2", "numeral": "^2.0.6", "nuxt": "^3.13.0", "nuxt-security": "^2.0.0", "nuxt-swiper": "^1.2.2", "pinia": "^2.2.2", "preline": "^2.5.1", "react": "^16.14.0", "sweetalert2": "^11.14.1", "swiper": "^11.1.14", "vue": "latest", "vue-awesome-paginate": "^1.2.0", "vue-inline-svg": "^3.1.4", "vue-router": "latest"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.12.1", "@types/bs58": "^4.0.4", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "autoprefixer": "^10.4.20", "daisyui": "^4.12.10", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "^5.8.2"}, "overrides": {"pbkdf2@<=3.1.3": "3.1.3"}}