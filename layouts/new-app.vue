<template>
  <!-- <div class="block w-screen h-screen xl:hidden">
    <div
      class="flex flex-col items-center justify-center h-full gap-2 p-5 text-center"
    >
      <h1 class="text-5xl tracking-[3px]">RESIZE</h1>
      <p>Your Browser resolution is currently not supported.</p>
      <p>Try resizing the window to experience the site.</p>
    </div>
  </div> -->

  <div class="relative block overflow-hidden lg:flex lg:flex-col lg:h-screen bg-base-100" data-theme="newtheme">
    <!-- sidebar -->
    <div class="hidden lg:block">
      <Sidebar />
    </div>

    <!-- header -->
    <div class="z-30 w-full bg-base-100">
      <div class="m-auto lg:pl-20">
        <div class="flex items-start justify-between gap-4 px-4 py-4 lg:px-10 lg:justify-end">
          <div class="flex items-center gap-4">
            <inline-svg @click.stop="showMobileSidebar" :src="menuIcon"
              class="w-8 h-[23.5px] lg:hidden flex-shrink-0"></inline-svg>
            <img :src="logo" class="w-6 lg:hidden" />
          </div>

          <div class="flex items-center gap-3">
            <div v-if="account.isConnected" class="flex items-start gap-3">
              <details class="hidden cursor-pointer dropdown xs:block" ref="network-switcher-button">
                <summary class="list-none">
                  <div
                    class="bg-primary/20 px-4 rounded-full flex items-center justify-center gap-2 h-10 min-h-10 w-[152px]">
                    <img class="min-w-[22px] w-[22px] h-[22px]" :src="networkInfoById[networkData.chainId].icon"
                      alt="" />
                    <h5>{{ networkInfoById[networkData.chainId].network.nativeCurrency.symbol }}</h5>
                    <inline-svg :src="arrowDown" class="flex-shrink-0 hidden lg:block"></inline-svg>
                  </div>
                </summary>
                <ul class="border rounded-lg menu border-textbox-border bg-base-100"
                  :style="{ boxShadow: '0px 0px 4px 0px #353333' }">
                  <li v-for="network in web3Store.networks" :key="network.id"><button class="whitespace-nowrap"
                      @click="networkData.switchNetwork(network); networkSwitcherRef.removeAttribute('open')">
                      <div class="flex items-center gap-2 flex-nowrap"><img class="min-w-[22px] w-[22px] h-[22px]"
                          :src="networkInfoById[network.id].icon" alt="" />{{ network.nativeCurrency.symbol }}</div>
                    </button>
                  </li>
                </ul>
              </details>
              <div class="flex items-center border rounded-full border-primary/20">
                <div class="hidden p-2 lg:px-4 whitespace-nowrap xs:block">
                  {{ numeral(accountNativeBalance).format("0,0.0000") }} {{
                    networkInfoById[networkData.chainId].network.nativeCurrency.symbol }}</div>
                <div
                  class="flex items-center justify-center h-10 gap-2 p-2 rounded-full cursor-pointer bg-primary/10 lg:px-4 min-h-10"
                  @click="web3Store.connectWallet()">
                  <h5>
                    {{ common.formatAddress(account.address) }}
                  </h5>
                </div>
              </div>
              <div>
                <ModalProfile :profileData="profileData"></ModalProfile>
              </div>
            </div>
            <!-- @click="web3Store.connectWallet()" -->
            <div v-else class="flex gap-2 lg:gap-4">
              <details class="cursor-pointer dropdown" ref="network-switcher-button">
                <summary class="list-none">
                  <div
                    class="bg-primary/20 p-2 px-4 rounded-full flex items-center justify-center gap-2 h-8 min-h-8 lg:h-10 lg:min-h-10 lg:w-[152px]">
                    <!-- need to duplicate because if not, they won't be reactive -->
                    <img v-if="networkData.chainId === avalanche.id" class="min-w-[22px] w-[22px] h-[22px]"
                      :src="networkInfoById[networkData.chainId].icon" alt="" />
                    <img v-if="networkData.chainId === sepolia.id" class="min-w-[22px] w-[22px] h-[22px]"
                      :src="networkInfoById[networkData.chainId].icon" alt="" />
                    <img v-if="networkData.chainId === mainnet.id" class="min-w-[22px] w-[22px] h-[22px]"
                      :src="networkInfoById[networkData.chainId].icon" alt="" />
                    <img v-if="[solana.id, solanaTestnet.id, solanaDevnet.id].includes(networkData.chainId)"
                      class="min-w-[22px] w-[22px] h-[22px]" :src="networkInfoById[networkData.chainId].icon" alt="" />
                    <h5>{{ networkInfoById[networkData.chainId].network.nativeCurrency.symbol }}</h5>
                    <inline-svg :src="arrowDown" class="flex-shrink-0 hidden lg:block"></inline-svg>
                  </div>
                </summary>
                <ul class="border rounded-lg menu border-textbox-border bg-base-100"
                  :style="{ boxShadow: '0px 0px 4px 0px #353333' }">
                  <li v-for="network in web3Store.networks" :key="network.id"><button class="whitespace-nowrap"
                      @click="networkData.switchNetwork(network); networkSwitcherRef.removeAttribute('open')">
                      <div class="flex items-center gap-2 flex-nowrap"><img class="min-w-[22px] w-[22px] h-[22px]"
                          :src="networkInfoById[network.id].icon" alt="" />{{ network.nativeCurrency.symbol }}</div>
                    </button>
                  </li>
                </ul>
              </details>

              <button
                class="btn btn-primary min-h-8 h-8 w-[92px] lg:h-10 lg:min-h-10 !rounded-full px-8 lg:w-[170px] font-medium"
                @click="showConnectModal">
                Connect
              </button>
            </div>
            <div class="hidden lg:block">
              <div v-if="isLoggedIn">
                <!-- <div
                  @click="showProfileModal"
                  class="flex items-center justify-center w-10 h-10 border rounded-full cursor-pointer border-primary/20"
                >
                  <inline-svg
                    class="w-[18px] h-[18px] fill-primary"
                    :src="profileIcon"
                  ></inline-svg>
                </div> -->
              </div>
            </div>
            <!-- <div class="flex gap-4" v-if="web3Store.userInfo.isConnected"></div> -->
          </div>
        </div>
      </div>
    </div>

    <!-- route  -->
    <div class="relative z-20 pt-24 overflow-hidden lg:flex-grow lg:overflow-auto lg:pl-20">
      <div class="lg:px-10">
        <div class="px-5 lg:px-10 max-w-[1310px] m-auto min-h-screen">
          <GenericModal :data="modalData" />

          <slot />
          <!-- <Footer /> -->
          <LandingFooter />
        </div>
      </div>
    </div>

    <div class="bg-footer absolute bottom-0 w-full min-w-[1835px] max-w-full z-10"></div>

    <ModalConnect ref="connectModal"></ModalConnect>
    <SidebarMobile @callConnectModalEvent="showConnectModal" ref="mobileSidebar"></SidebarMobile>
  </div>
</template>

<script setup>
import { useWeb3Store, networkInfoById } from "../stores/web3";
import profileIcon from "~/assets/images_new/icons/profile.svg";
import menuIcon from "~/assets/images_new/header/menu.svg";
import logo from "~/assets/images_new/header/logo.webp";

import arrowDown from "~/assets/images_new/icons/chevron-down.svg";

import InlineSvg from "vue-inline-svg";
import numeral from "numeral";

import { version } from "../package.json";
import { useAppKitEvents, useAppKitState, useAppKitNetwork, useAppKitAccount } from "@reown/appkit/vue";
import { avalanche, mainnet, sepolia, solana, solanaDevnet, solanaTestnet } from "@reown/appkit/networks";
import { Buffer } from "buffer";
import GenericModal from "~/components/GenericModal.vue";
globalThis.Buffer = globalThis.Buffer || Buffer

useHead({
  link: [
    {
      rel: "shortcut icon",
      href: "https://files-secondswap.secondswap.io/image_2swap.png"
    }
  ]
});

const account = useAppKitAccount();
const newWeb3Store = useNewWeb3Store();
const { web3Kit, accountNativeBalance } = storeToRefs(newWeb3Store);
const web3Store = useWeb3Store();
const { isLoggedIn } = storeToRefs(newWeb3Store);
const networkData = useAppKitNetwork();

const modalData = ref({})
const modalIdMapping = {
  loading: "loading_modal",
  error: "error_modal",
  success: "success_modal"
}
const closeModal = () => {
  const element = document.getElementById(modalIdMapping[modalData.value.type])
  if (element) element.removeAttribute("open")
  modalData.value = {}
}
const openModal = (data) => {
  closeModal();
  modalData.value = data
  // wait for the modal element to be rendered
  setTimeout(() => {
    const element = document.getElementById(modalIdMapping[data.type])
    console.log(element)
    if (element) element.showModal()
  }, 10)
}

provide("openModal", openModal)
provide("closeModal", closeModal)

const events = useAppKitEvents();
const state = useAppKitState();
const connectModal = ref(null);
const mobileSidebar = ref(null);

const profileData = ref(null);
const networkSwitcherRef = useTemplateRef("network-switcher-button")

const route = useRoute();

const { isMobile } = useIsMobile();

watch(events, async (newValue) => {
  console.log("watch event newValue", newValue.data.event);
  console.log("watch event object", newValue);

  switch (events.data.event) {
    case "MODAL_LOADED":
      await web3Store.getAccountInfo();

      if (isLoggedIn.value) {
        getProfile();
      }
      console.log("get profile called from modal loaded");
      // isLoading.value = false;
      break;

    case "CONNECT_SUCCESS":
      await web3Store.getAccountInfo();

      if (isLoggedIn.value) {
        getProfile();
      } else {
        // signLogin();
        connectModal.value.handleClick();
        document.getElementById("connectModal").checked = true;
      }

      break;

    case "MODAL_CLOSE":
      // console.log(
      //   "watch modal close connect status",
      //   events.data.properties.connected
      // );

      if (!events.data.properties.connected) {
        web3Store.disconnect();
      } else {
        await web3Store.getAccountInfo();

        if (isLoggedIn.value) {
          getProfile();
        } else {
          // signLogin();
          connectModal.value.handleClick();
          document.getElementById("connectModal").checked = true;
        }
      }

      break;
    case "SWITCH_NETWORK":
      web3Store.getAccountInfo();
      break;
    case "SELECT_WALLET":
      if (isMobile.value && !window.solana) {
        const encodedURL = encodeURIComponent(window.location.href);
        const encodedRefURL = encodeURIComponent(useRuntimeConfig().public.baseUrl);
        if (events.data.properties.name === "Phantom") {
          useSweetAlertStore().showConfirmAlert(
            `Your browser does not support Phantom Wallet. Please use Phantom’s built-in browser to connect your wallet.`,
            null,
            () => window.open(`https://phantom.app/ul/browse/${encodedURL}?ref=${encodedRefURL}`),
            `Open Phantom`
          );
        } else if (events.data.properties.name === "Solflare") {
          useSweetAlertStore().showConfirmAlert(
            `Your browser does not support Solflare Wallet. Please use Solflare’s built-in browser to connect your wallet.`,
            null,
            () => window.open(`https://solflare.com/ul/v1/browse/${encodedURL}?ref=${encodedRefURL}`),
            `Open Solflare`
          );
        }
      }
      break;
  }
});

async function getProfile() {
  const res = await api.apiCall("GET", "/user");
  console.log("get profile response", res.data.message);
  profileData.value = res.data.message;
  console.log("profile data", profileData);
  // profileData.value = res.data.message;
  // profileEmail.value = res.data.message.email;
  // profileName.value = res.data.message.username;
  // profileTelegram.value = res.data.message.telegram;
  // profileWhatsapp.value = res.data.message.whatsapp;
  //   if(res.data.message.image != null){
  //     imgPreview.value = res.data.message.image
  //   }else{
  //     imgPreview.value = imgDefault
  //   }
}

function showProfileModal() {
  console.log("profile data in modal", profileData);

  if (isLoggedIn.value) {
    //getProfile();
    profileModal.showModal();
  }
}

function showConnectModal() {
  connectModal.value.handleClick();
  document.getElementById("connectModal").checked = true;
  console.log("connect modal called");
}

function showMobileSidebar() {
  document.getElementById("mobileSidebar").checked = true;
  mobileSidebar.value.handleClick();
}

onMounted(() => {
  window.$crisp = [];
  window.CRISP_WEBSITE_ID = "115f4031-f617-463f-905d-8023317a0d55";
  (function () {
    let d = document;
    let s = d.createElement("script");
    s.src = "https://client.crisp.chat/l.js";
    s.async = 1;
    d.getElementsByTagName("head")[0].appendChild(s);

    // window.$crisp.push(["do", "chat:hide"]);

    window.$crisp.push(["config", "color:mode", ["dark"]]);

    // window.$crisp.push(["config", "position:reverse", [true]]);
    window.$crisp.push(["do", "chat:hide"]);
  })();

  // document.getElementById("connectModal").checked = true;
});
</script>

<style scoped>
.bg-footer {
  background-image: url("~/assets/images_new/common/2s-footer-bg_1_5x.webp");
  background-repeat: no-repeat;
  background-size: 2230px 1424px;
  background-position-y: -10rem;
  background-position-x: -85rem;
  /* background-size: cover; */
  /* background-position-y: -10rem;
  background-position-x: -68rem; */
  min-height: 100vh;
  /* min-height: calc(100vh - 96px); */
}

@media (min-width: 900px) {
  .bg-footer {
    background-size: 100% 100%;
    background-position-y: 15rem;
    background-position-x: 0;
  }
}

@media (min-width: 1600px) {
  .bg-footer {
    background-size: 100% 100%;
    background-position-y: 20rem;
    background-position-x: 0;
  }
}

/* Add these new styles for Crisp chat */
:global(.crisp-client) {
  --crisp-position-reverse: 1 !important;
}

:global(.crisp-client .cc-kv6t) {
  margin: 20px 20px 20px 20px !important;
  right: auto !important;
  left: 0 !important;
}
</style>
