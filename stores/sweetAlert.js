import { defineStore } from 'pinia'
import Swal from 'sweetalert2'
import lottie from 'lottie-web'
import errorIcon from '~/assets/animation/error.json'
import successIcon from '~/assets/animation/success.json'
import timeOutIcon from '~/assets/animation/timeout.json'
import alertIcon from '~/assets/animation/info.json'


export const useSweetAlertStore = defineStore('sweetalert', () => {
    const SwalCustom = Swal.mixin({
        // background: '#38336B',
        color: 'white',
        border: 'none',
        customClass: {
            popup: 'bg-modal rounded-3xl w-[408px] border border-textbox-border shadow-lg shadow-[#FFFFFF14]',
            icon: 'border-none icon-height',
            title: 'text-2xl capitalize -mt-5',
            confirmButton: 'w-full h-40 bg-transparent min-h-40 !rounded-3xl py-0 !text-white text-2sm',
            denyButton: 'btn !h-10 !min-h-10 !px-10 !rounded-3xl py-0 !w-full bg-transparent !text-[#7CD3F8] ',
            // denyButton: 'btn !btn-error !h-10 !min-h-10 !px-10 !rounded-full py-0 !w-3/4 !text-black',
            cancelButton: 'text-sm w-full',
            closeButton: 'text-white w-full',
            loader: '!hidden',
            htmlContainer: 'w-[408px] text-2sm',
            actions: 'w-full'
        }
    })

    const SwalBuySuccessCustom = Swal.mixin({
        // background: '#38336B',
        color: 'white',
        border: 'none',
        customClass: {
            popup: 'bg-modal rounded-3xl',
            icon: 'border-none icon-height',
            title: 'text-2xl capitalize -mt-5',
            confirmButton: 'w-full h-40 min-h-40 !rounded-3xl py-0 text-2sm buy-success-btn',
            denyButton: 'btn !btn-error !h-10 !min-h-10 !px-10 !rounded-full py-0 !w-3/4 !text-black',
            cancelButton: 'bg-modal text-primary text-sm !hover:bg-modal w-full back-to-market-btn',
            closeButton: 'text-white w-full',
            loader: '!hidden',
            htmlContainer: '!text-gray-400 !text-sm w-[408px]',
            actions: 'w-full'
        }
    })

    const icons = {
        success: successIcon,
        error: errorIcon,
        info: `<svg class="w-[75px]" viewBox="0 0 144 144" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M72 143.5C86.1413 143.5 99.9651 139.307 111.723 131.45C123.481 123.594 132.646 112.427 138.057 99.3619C143.469 86.297 144.885 71.9207 142.126 58.051C139.367 44.1814 132.558 31.4413 122.558 21.4419C112.559 11.4424 99.8186 4.6327 85.9489 1.87385C72.0793 -0.884985 57.703 0.530937 44.6381 5.9426C31.5732 11.3543 20.4064 20.5186 12.5499 32.2767C4.69338 44.0348 0.499977 57.8586 0.499976 72C0.519994 90.9568 8.05944 109.131 21.4639 122.536C34.8685 135.941 53.0431 143.48 72 143.5ZM77.5 105C77.5 106.459 76.9205 107.858 75.8891 108.889C74.8576 109.921 73.4587 110.5 72 110.5C70.5413 110.5 69.1423 109.921 68.1109 108.889C67.0794 107.858 66.5 106.459 66.5 105L66.5 66.5C66.5 65.0413 67.0794 63.6424 68.1109 62.6109C69.1423 61.5795 70.5413 61 72 61C73.4587 61 74.8576 61.5795 75.8891 62.6109C76.9205 63.6424 77.5 65.0413 77.5 66.5L77.5 105ZM72 33.5C73.6317 33.5 75.2267 33.9839 76.5834 34.8904C77.9401 35.7969 78.9976 37.0854 79.622 38.5929C80.2464 40.1004 80.4098 41.7591 80.0915 43.3595C79.7731 44.9598 78.9874 46.4298 77.8336 47.5836C76.6798 48.7374 75.2098 49.5231 73.6095 49.8415C72.0091 50.1598 70.3503 49.9964 68.8428 49.372C67.3354 48.7476 66.0469 47.6901 65.1404 46.3334C64.2338 44.9767 63.75 43.3817 63.75 41.75C63.75 39.562 64.6192 37.4635 66.1664 35.9164C67.7135 34.3692 69.8119 33.5 72 33.5Z" fill="#48C3F8"/>
            </svg>
            `,
        loading: `<div class='loading-animation'></div>`,
        timeout: timeOutIcon,
        alert: alertIcon
    }

    function close() {
        Swal.close()
    }

    function showAlert(title, text, icon) {
        console.log("showAlert", useSweetAlertStore().icons[icon]);
        return SwalCustom.fire({
            iconHtml: '<div id="lottie-container"></div>',
            title: title,
            html: `<div style="white-space: pre;">${text}</div>`,
            showConfirmButton: true,
            showCloseButton: true,
            didOpen: () => {
                if (typeof document !== 'undefined') {
                    lottie.loadAnimation({
                        container: document.getElementById('lottie-container'),
                        renderer: 'svg',
                        loop: false,
                        autoplay: true,
                        animationData: useSweetAlertStore().icons[icon],
                        rendererSettings: {
                            preserveAspectRatio: 'xMidYMid meet'
                        }
                    });
                }
            },
        })
    }

    async function showSuccessAlert({
        title,
        content,
        confirmButton,
        onConfirm,
        footer,
    }) {
        return Swal.mixin({
            // background: '#38336B',
            color: 'white',
            border: 'none',
            customClass: {
                popup: 'bg-modal rounded-3xl w-[408px] border border-textbox-border shadow-lg shadow-[#FFFFFF14]',
                icon: 'border-none icon-height',
                title: 'text-2xl capitalize -mt-5',
                footer: 'border-none mt-0 text-primary text-sm',
                confirmButton: 'w-full h-40 bg-primary min-h-40 !rounded-3xl py-0 !text-base-100 text-2sm',
                denyButton: 'btn !h-10 !min-h-10 !px-10 !rounded-3xl py-0 !w-full bg-transparent !text-[#7CD3F8] ',
                // denyButton: 'btn !btn-error !h-10 !min-h-10 !px-10 !rounded-full py-0 !w-3/4 !text-black',
                cancelButton: 'text-sm w-full',
                closeButton: 'text-white w-full',
                loader: '!hidden',
                // htmlContainer: 'w-[408px] text-2sm',
                actions: 'w-full',
            }
        }).fire({
            iconHtml: '<div id="lottie-container"></div>',
            title: title,
            footer,
            html: content,
            showConfirmButton: true,
            showCloseButton: true,
            confirmButtonText: confirmButton,
            didOpen: () => {
                if (typeof document !== 'undefined') {
                    lottie.loadAnimation({
                        container: document.getElementById('lottie-container'),
                        renderer: 'svg',
                        loop: false,
                        autoplay: true,
                        animationData: useSweetAlertStore().icons["success"],
                        rendererSettings: {
                            preserveAspectRatio: 'xMidYMid meet'
                        }
                    });
                }
            },
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                onConfirm()
            }
        });
    }

    function showLoadingAlert(title, text) {
        return SwalCustom.fire({
            title: title,
            text: text,
            showConfirmButton: false,
            iconHtml: useSweetAlertStore().icons['loading'],
            didOpen: () => {
                SwalCustom.showLoading()
            },
            allowOutsideClick: false,
        })
    }

    async function showConfirmAlert(text, data = null, callback, title = 'Delist Confirmation', isTimeout = false, icon, confirmButtonText = "Confirm", showDenyButton = true, denyButtonText = "Cancel") {
        return SwalCustom.fire({
            title: title,
            text: text,
            showDenyButton: showDenyButton,
            confirmButtonText: confirmButtonText,
            denyButtonText: denyButtonText,
            iconHtml: '<div id="lottie-container"></div>',
            didOpen: () => {
                if (typeof document !== 'undefined') {
                    let confirmIcon = {
                        true: useSweetAlertStore().icons['timeout'],
                        false: icon ? useSweetAlertStore().icons[icon] : useSweetAlertStore().icons['alert']
                    }

                    isTimeout ? useSweetAlertStore().icons['timeout'] : useSweetAlertStore().icons['alert'];
                    lottie.loadAnimation({
                        container: document.getElementById('lottie-container'),
                        renderer: 'svg',
                        loop: false,
                        autoplay: true,
                        animationData: confirmIcon[isTimeout],
                        rendererSettings: {
                            preserveAspectRatio: 'xMidYMid meet'
                        }
                    });
                }
            },
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                callback(data)
            }
        });
    }

    async function showBuySuccessAlert(title, text, icon) {
        return SwalBuySuccessCustom.fire({
            title: title,
            html: text,
            icon: icon,
            iconHtml: '<div id="lottie-container"></div>',
            showCancelButton: true,
            showCloseButton: true,
            confirmButtonText: "View in My Assets",
            cancelButtonText: "Back to Market",
            didOpen: () => {
                if (typeof document !== 'undefined') {
                    lottie.loadAnimation({
                        container: document.getElementById('lottie-container'),
                        renderer: 'svg',
                        loop: false,
                        autoplay: true,
                        animationData: useSweetAlertStore().icons[icon]
                    });
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                navigateTo('/myassets');
            } else if (result.isDismissed) {
                navigateTo('/');
            }
        });
    }

    return { icons, showAlert, showLoadingAlert, showConfirmAlert, showBuySuccessAlert, showSuccessAlert, close }
})

