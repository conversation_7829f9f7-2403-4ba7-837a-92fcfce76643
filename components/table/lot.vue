<template>
  <table class="table border-separate table-fixed text-2sm border-spacing-y-3">
    <thead class="text-sm">
      <tr class="border-b-0">
        <th class="w-[20%] pl-5 font-normal">Lot</th>
        <th class="w-[15%] font-normal">Amount Listed</th>
        <th class="w-[15%] font-normal">
          <div class="flex">
            <h5>Price</h5>
            <inline-svg
              @click="toggleSort('LISTED_PRICE')"
              :class="`${
                order === 'ASC' && sortBy == 'LISTED_PRICE'
                  ? 'sort-asc'
                  : order === 'DESC' && sortBy == 'LISTED_PRICE'
                  ? 'sort-desc'
                  : 'sort-icon'
              } 
              w-[16px] h-[16px] cursor-pointer`"
              :src="sortIcon"
            ></inline-svg>
          </div>
        </th>
        <th class="w-[15%] font-normal">
          <div class="flex">
            <h5>Best Price</h5>
            <inline-svg
              @click="toggleSort('BEST_PRICE')"
              :class="`${
                order === 'ASC' && sortBy == 'BEST_PRICE'
                  ? 'sort-asc'
                  : order === 'DESC' && sortBy == 'BEST_PRICE'
                  ? 'sort-desc'
                  : 'sort-icon'
              } 
              w-[16px] h-[16px] cursor-pointer`"
              :src="sortIcon"
            ></inline-svg>
          </div>
        </th>
        <th class="w-[12%] font-normal">
          <div class="flex">
            <h5>Discount</h5>
            <inline-svg
              @click="toggleSort('DISCOUNT')"
              :class="`${
                order === 'ASC' && sortBy == 'DISCOUNT'
                  ? 'sort-asc'
                  : order === 'DESC' && sortBy == 'DISCOUNT'
                  ? 'sort-desc'
                  : 'sort-icon'
              } 
              w-[16px] h-[16px] cursor-pointer`"
              :src="sortIcon"
            ></inline-svg>
          </div>
        </th>
        <th class="w-[12%] font-normal">
          <div class="flex">
            <h5>Unlock Start</h5>
            <inline-svg
              @click="toggleSort('UNLOCK_SOON')"
              :class="`${
                order === 'ASC' && sortBy == 'UNLOCK_SOON'
                  ? 'sort-asc'
                  : order === 'DESC' && sortBy == 'UNLOCK_SOON'
                  ? 'sort-desc'
                  : 'sort-icon'
              } 
              w-[16px] h-[16px] cursor-pointer`"
              :src="sortIcon"
            ></inline-svg>
          </div>
        </th>
        <th class="w-[7%] text-right p-0 font-normal">% Filled</th>
        <th class="w-[14%] text-right pr-2"></th>
      </tr>
    </thead>
    <tbody>
      <tr v-if="!lots || lots.length == 0">
        <td colspan="7">
          <NoData :content="props.noDataContent"></NoData>
        </td>
      </tr>
      <tr
        v-else
        @click="toLotInfo(lot)"
        v-for="(lot, index) in lots.filter(lot => !network || lot.network_symbol === network)"
        class="transition-all duration-300 border-b-0 hover:bg-white/10"
      >
        <td class="pr-5 rounded-s-lg">
          <div class="flex items-center gap-4">
            <div class="relative w-[44px] h-[44px] flex-shrink-0">
              <img
                class="w-full h-full rounded-full"
                :src="lot.token_image"
                alt="token-img"
              />

              <img
                class="absolute bottom-0 w-[16px] h-[16px]"
                :src="lot.network_image != 'undefined' ? lot.network_image : null"
                alt="network-img"
              />
            </div>

            <div>
              <h3>{{ lot.token_ticker }}</h3>
              <div class="flex items-center gap-1">
                <h5
                  class="text-sm text-white/50 truncate max-w-[50px] 2lg:truncate-none 2lg:max-w-full"
                >
                  {{ lot.token_name }}
                </h5>
              </div>
            </div>
          </div>
        </td>

        <td class="pr-5">{{ formatToken(divideNumberUsingDecimals(lot.remaining_listing, lot.token_decimal)) }}</td>

        <td class="pr-5">
          <h5>
            {{
              myAssets
                ? formatUSDT(
                    divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS)
                  )
                : formatUSDT(
                    divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS)
                  )
            }}
            USDT
          </h5>
          <h5
            v-if="lot.twf_hour_changes != undefined"
            :class="`text-sm ${twfChangesInfo[lot.twf_hour_changes * 1 > 0].color}`"
          >
            {{ twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
            }}{{ lot.twf_hour_changes.replace("-", "") }}%
          </h5>
        </td>

        <td class="pr-5">
          <h5>
            {{
              numeral(
                divideNumberUsingDecimals(
                  lot.listed_price,
                  USDT_DECIMALS
                ).multipliedBy(toBigNumber(1).minus(toBigNumber(lot.discount_pct).dividedBy(100))).toString()
              ).format("0,0.00a", Math.floor)
            }}
            USDT
          </h5>
        </td>

        <td class="pr-5">
          <h5>
            Up to
            {{ formatToken(lot.discount_pct) }}%
          </h5>
        </td>

        <td class="pr-5">
          <h5 v-if="lot.start_date != 'undefined' && lot.cliff_duration != 'undefined'" class="flex flex-col items-start">
            {{
              lot.plan_function === "CYCLE" || lot.vesting_type === "CYCLE"
                ? $dayjs(lot.start_date)
                    // .add(lot.cliff_duration, timeUnit)
                    .add(lot.duration, timeUnit)
                    .format("DD/MM/YYYY")
                : $dayjs(lot.start_date)
                    // .add(lot.cliff_duration, timeUnit)
                    .format("DD/MM/YYYY")
            }}

            <span class="text-xs text-white/50">{{
              getCountDown(
                lot.plan_function === "CYCLE" || lot.vesting_type === "CYCLE"
                  ? $dayjs(lot.start_date)
                      // .add(lot.cliff_duration, timeUnit)
                      .add(lot.duration, timeUnit)
                      .format("x") * 1
                  : $dayjs(lot.start_date)
                      // .add(lot.cliff_duration, timeUnit)
                      .format("x") * 1
              )
            }}</span>
          </h5>
        </td>

        <td class="p-0 text-right">
          {{
            lot.listing_type == 0
              ? `${numeral(
                  ((lot.total_listing - lot.remaining_listing) / lot.total_listing) * 100
                ).format("0.00")}%`
              : "Single Fill"
          }}
        </td>
        <td class="font-normal text-right rounded-e-lg">
          <button
            @click.stop="() => handleClickAction(lot)"
            class="btn btn-primary h-[26px] min-h-[26px] min-w-[68px] !rounded-2sm text-2sm px-[6px]"
          >
            {{ isLoggedIn ? (myAssets ? "Delist" : "Buy") : "Connect" }}
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script setup>
import numeral from "numeral";
import sortIcon from "~/assets/images_new/icons/sort.svg";
import InlineSvg from "vue-inline-svg";
import { USDT_DECIMALS, TIME_UNIT } from "~/utils/const";
import { divideNumberUsingDecimals, toBigNumber, formatToken, formatUSDT } from "~/utils/number";

const { $dayjs } = useNuxtApp();
const timeUnit = TIME_UNIT;

const props = defineProps({
  lots: Object,
  getCountDown: Function,
  getAllLots: Function,
  noDataContent: String,
  twfChangesInfo: Object,
  myAssets: Boolean,
  network: String,
});

const emit = defineEmits(["showBuyModalEvent"]);

const web3Store = useWeb3Store();
const newWeb3Store = useNewWeb3Store();
const { isLoggedIn } = storeToRefs(newWeb3Store);
const { refetch } = useMarketplace();

const handleClickAction = async (lot) => {
  const tokenNetworkSymbol = lot.network_symbol;

  const refetched = await refetch()
  const foundLot = refetched.allLots?.find(refetchedLot => {
    // TECH DEPT: we need a better unique identifier to find the lot
    return common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id) === common.formatLotId(refetchedLot.token_ticker, refetchedLot.display_id, refetchedLot.list_id) && lot.token_id === refetchedLot.token_id
  })

  if (router.currentRoute.value.name == "myassets") {
    emit("showBuyModalEvent", { lot: foundLot || lot });
    return;
  }

  await web3Store.switchNetwork(tokenNetworkSymbol)
  emit("showBuyModalEvent", { lot: foundLot || lot });
};

const sortBy = ref(null);
const order = ref(null);
const lots = ref(null);

const router = useRouter();

async function toLotInfo(lot) {
  router.push(`/${lot.plan_id}-${lot.display_id}-${lot.list_id}?network=${lot.network_symbol}`);
}

function toggleSort(sort) {
  if (order.value == "ASC") {
    order.value = "DESC";
  } else {
    order.value = "ASC";
  }
  sortBy.value = sort;
}

watch(sortBy, async (newValue, oldValue) => {
  order.value = "ASC";
  lots.value = await props.getAllLots(newValue, order.value);
  console.log("sort from lot", lots.value);
});

watch(order, async (newValue, oldValue) => {
  lots.value = await props.getAllLots(sortBy.value, newValue);
});

watch(props, (newValue) => {
  lots.value = props.lots;
});

onMounted(async () => {
  lots.value = props.lots;
});
</script>

<style scoped>
.sort-icon :deep(path),
.sort-asc :deep(path:last-of-type),
.sort-desc :deep(path:first-of-type) {
  fill: #a1a1a1;
}
.sort-asc :deep(path:first-of-type) {
  fill: white;
}
.sort-desc :deep(path:last-of-type) {
  fill: white;
}
</style>
